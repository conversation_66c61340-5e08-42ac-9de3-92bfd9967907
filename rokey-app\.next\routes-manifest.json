{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=30, stale-while-revalidate=60"}, {"key": "X-Content-Type-Options", "value": "nosniff"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/_next/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}], "regex": "^/manifest\\.json(?:/)?$"}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}], "regex": "^/sw\\.js(?:/)?$"}, {"source": "/", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}], "regex": "^/(?:/)?$"}, {"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "X-Frame-Options", "value": "DENY"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/custom-configs/[configId]", "regex": "^/api/custom\\-configs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)(?:/)?$"}, {"page": "/api/custom-configs/[configId]/default-chat-key", "regex": "^/api/custom\\-configs/([^/]+?)/default\\-chat\\-key(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/default\\-chat\\-key(?:/)?$"}, {"page": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "regex": "^/api/custom\\-configs/([^/]+?)/default\\-key\\-handler/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId", "nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/default\\-key\\-handler/(?<nxtPapiKeyId>[^/]+?)(?:/)?$"}, {"page": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "regex": "^/api/custom\\-configs/([^/]+?)/keys/([^/]+?)/complexity\\-assignments(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId", "nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/keys/(?<nxtPapiKeyId>[^/]+?)/complexity\\-assignments(?:/)?$"}, {"page": "/api/custom-configs/[configId]/routing", "regex": "^/api/custom\\-configs/([^/]+?)/routing(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/api/custom\\-configs/(?<nxtPconfigId>[^/]+?)/routing(?:/)?$"}, {"page": "/api/documents/[documentId]", "regex": "^/api/documents/([^/]+?)(?:/)?$", "routeKeys": {"nxtPdocumentId": "nxtPdocumentId"}, "namedRegex": "^/api/documents/(?<nxtPdocumentId>[^/]+?)(?:/)?$"}, {"page": "/api/keys/[apiKeyId]", "regex": "^/api/keys/([^/]+?)(?:/)?$", "routeKeys": {"nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/keys/(?<nxtPapiKeyId>[^/]+?)(?:/)?$"}, {"page": "/api/keys/[apiKeyId]/roles", "regex": "^/api/keys/([^/]+?)/roles(?:/)?$", "routeKeys": {"nxtPapiKeyId": "nxtPapiKeyId"}, "namedRegex": "^/api/keys/(?<nxtPapiKeyId>[^/]+?)/roles(?:/)?$"}, {"page": "/api/keys/[apiKeyId]/roles/[roleName]", "regex": "^/api/keys/([^/]+?)/roles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPapiKeyId": "nxtPapiKeyId", "nxtProleName": "nxtProleName"}, "namedRegex": "^/api/keys/(?<nxtPapiKeyId>[^/]+?)/roles/(?<nxtProleName>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/status/[executionId]", "regex": "^/api/orchestration/status/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/status/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/stream/[executionId]", "regex": "^/api/orchestration/stream/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/stream/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/synthesis-fallback/[executionId]", "regex": "^/api/orchestration/synthesis\\-fallback/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/synthesis\\-fallback/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/synthesis-stream/[executionId]", "regex": "^/api/orchestration/synthesis\\-stream/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/synthesis\\-stream/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/orchestration/synthesis-stream-direct/[executionId]", "regex": "^/api/orchestration/synthesis\\-stream\\-direct/([^/]+?)(?:/)?$", "routeKeys": {"nxtPexecutionId": "nxtPexecutionId"}, "namedRegex": "^/api/orchestration/synthesis\\-stream\\-direct/(?<nxtPexecutionId>[^/]+?)(?:/)?$"}, {"page": "/api/user/custom-roles/[customRoleId]", "regex": "^/api/user/custom\\-roles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcustomRoleId": "nxtPcustomRoleId"}, "namedRegex": "^/api/user/custom\\-roles/(?<nxtPcustomRoleId>[^/]+?)(?:/)?$"}, {"page": "/my-models/[configId]", "regex": "^/my\\-models/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/my\\-models/(?<nxtPconfigId>[^/]+?)(?:/)?$"}, {"page": "/routing-setup/[configId]", "regex": "^/routing\\-setup/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconfigId": "nxtPconfigId"}, "namedRegex": "^/routing\\-setup/(?<nxtPconfigId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/add-keys", "regex": "^/add\\-keys(?:/)?$", "routeKeys": {}, "namedRegex": "^/add\\-keys(?:/)?$"}, {"page": "/analytics", "regex": "^/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/analytics(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/auth/verify-email", "regex": "^/auth/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/verify\\-email(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/debug-session", "regex": "^/debug\\-session(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-session(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/features", "regex": "^/features(?:/)?$", "routeKeys": {}, "namedRegex": "^/features(?:/)?$"}, {"page": "/logs", "regex": "^/logs(?:/)?$", "routeKeys": {}, "namedRegex": "^/logs(?:/)?$"}, {"page": "/my-models", "regex": "^/my\\-models(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-models(?:/)?$"}, {"page": "/playground", "regex": "^/playground(?:/)?$", "routeKeys": {}, "namedRegex": "^/playground(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/routing-setup", "regex": "^/routing\\-setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/routing\\-setup(?:/)?$"}, {"page": "/training", "regex": "^/training(?:/)?$", "routeKeys": {}, "namedRegex": "^/training(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}