(()=>{var e={};e.id=1666,e.ids=[1489,1666],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o,createSupabaseServerClientOnRequest:()=>a});var s=r(34386),i=r(44999);async function a(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function o(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>d,w:()=>u});var s=r(55511),i=r.n(s);let a="aes-256-gcm",o=process.env.ROKEY_ENCRYPTION_KEY;if(!o||64!==o.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let n=Buffer.from(o,"hex");function u(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=i().randomBytes(12),r=i().createCipheriv(a,n,t),s=r.update(e,"utf8","hex");s+=r.final("hex");let o=r.getAuthTag();return`${t.toString("hex")}:${o.toString("hex")}:${s}`}function d(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=Buffer.from(t[0],"hex"),s=Buffer.from(t[1],"hex"),o=t[2];if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==s.length)throw Error("Invalid authTag length. Expected 16 bytes.");let u=i().createDecipheriv(a,n,r);u.setAuthTag(s);let d=u.update(o,"hex","utf8");return d+u.final("utf8")}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88502:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>_,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>f,POST:()=>l,PUT:()=>m});var i=r(96559),a=r(48088),o=r(37719),n=r(32190),u=r(2507),d=r(56534),p=r(55511),c=r.n(p);async function l(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return n.NextResponse.json({error:"Unauthorized: You must be logged in to create API keys."},{status:401});let i=r.user;try{let{custom_api_config_id:r,provider:s,predefined_model_id:a,api_key_raw:o,label:u,temperature:p=1}=await e.json();if(!r||!s||!a||!o||!u)return n.NextResponse.json({error:"Missing required fields: custom_api_config_id, provider, predefined_model_id, api_key_raw, label"},{status:400});if(p<0||p>2)return n.NextResponse.json({error:"Temperature must be between 0.0 and 2.0"},{status:400});if("string"!=typeof o||0===o.trim().length)return n.NextResponse.json({error:"API key cannot be empty."},{status:400});let l=(0,d.w)(o),f=c().createHash("sha256").update(o).digest("hex"),m={custom_api_config_id:r,provider:s,predefined_model_id:a,encrypted_api_key:l,label:u,api_key_hash:f,status:"active",is_default_general_chat_model:!1,temperature:p,user_id:i.id},{data:_,error:g}=await t.from("api_keys").insert(m).select().single();if(g){if("23503"===g.code)return n.NextResponse.json({error:"Invalid custom_api_config_id or predefined_model_id.",details:g.message},{status:400});if("23505"===g.code){if(g.message.includes("unique_model_per_config"))return n.NextResponse.json({error:"This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.",details:g.message},{status:409});return n.NextResponse.json({error:"A unique constraint was violated.",details:g.message},{status:409})}return n.NextResponse.json({error:"Failed to save API key",details:g.message},{status:500})}if(_){let{data:e,error:s}=await t.from("api_keys").select("id").eq("custom_api_config_id",r).eq("user_id",i.id).eq("is_default_general_chat_model",!0).neq("id",_.id).limit(1);if(s);else if(!e||0===e.length){let{data:e,error:r}=await t.from("api_keys").update({is_default_general_chat_model:!0}).eq("id",_.id).eq("user_id",i.id).select().single();if(!r)return n.NextResponse.json(e,{status:201})}}return n.NextResponse.json(_,{status:201})}catch(e){if("SyntaxError"===e.name)return n.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});if(e.message.includes("Invalid ROKEY_ENCRYPTION_KEY")||e.message.includes("Encryption input must be a non-empty string"))return n.NextResponse.json({error:"Server-side encryption error",details:e.message},{status:500});return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function f(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return n.NextResponse.json({error:"Unauthorized: You must be logged in to view API keys."},{status:401});let i=r.user,{searchParams:a}=new URL(e.url),o=a.get("custom_config_id");if(!o)return n.NextResponse.json({error:"custom_config_id query parameter is required"},{status:400});try{let{data:e,error:r}=await t.from("api_keys").select("id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model").eq("custom_api_config_id",o).eq("user_id",i.id).order("created_at",{ascending:!1});if(r)return n.NextResponse.json({error:"Failed to fetch API keys",details:r.message},{status:500});let s=(e||[]).map(e=>({id:e.id,custom_api_config_id:e.custom_api_config_id,provider:e.provider,predefined_model_id:e.predefined_model_id,label:e.label,status:e.status,temperature:e.temperature,created_at:e.created_at,last_used_at:e.last_used_at,is_default_general_chat_model:e.is_default_general_chat_model}));return n.NextResponse.json(s,{status:200})}catch(e){return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function m(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return n.NextResponse.json({error:"Unauthorized: You must be logged in to update API keys."},{status:401});let i=r.user,{searchParams:a}=new URL(e.url),o=a.get("id");if(!o)return n.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{temperature:r,predefined_model_id:s}=await e.json(),a={};if(void 0!==r){if(r<0||r>2)return n.NextResponse.json({error:"Temperature must be between 0.0 and 2.0"},{status:400});a.temperature=r}if(void 0!==s){if("string"!=typeof s||0===s.trim().length)return n.NextResponse.json({error:"Model ID must be a non-empty string"},{status:400});a.predefined_model_id=s}if(0===Object.keys(a).length)return n.NextResponse.json({error:"No valid fields provided for update"},{status:400});let{data:u,error:d}=await t.from("api_keys").update(a).eq("id",o).eq("user_id",i.id).select("id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model").single();if(d){if("23505"===d.code&&d.message.includes("unique_model_per_config"))return n.NextResponse.json({error:"This model is already configured in this setup. Each model can only be used once per configuration.",details:d.message},{status:409});return n.NextResponse.json({error:"Failed to update API key",details:d.message},{status:500})}return n.NextResponse.json(u,{status:200})}catch(e){if("SyntaxError"===e.name)return n.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/keys/route",pathname:"/api/keys",filename:"route",bundlePath:"app/api/keys/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:y}=_;function x(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(88502));module.exports=s})();