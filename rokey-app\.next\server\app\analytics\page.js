(()=>{var e={};e.id=1745,e.ids=[1745],e.modules={2969:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19153:(e,t,s)=>{Promise.resolve().then(s.bind(s,64609))},19682:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"}))})},21031:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx","default")},23425:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),o=s(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let d={children:["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21031)),"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,28044)),"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28044:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(47417);function n(){return(0,r.jsx)(a.AnalyticsSkeleton,{})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40877:(e,t,s)=>{Promise.resolve().then(s.bind(s,47417))},45700:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))})},47417:(e,t,s)=>{"use strict";s.d(t,{AnalyticsSkeleton:()=>i,ConfigSelectorSkeleton:()=>n,MessageSkeleton:()=>a,MyModelsSkeleton:()=>l,RoutingSetupSkeleton:()=>o});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let l=(0,r.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),o=(0,r.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},54001:(e,t,s)=>{Promise.resolve().then(s.bind(s,21031))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58089:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},59168:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},62392:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64364:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))})},64609:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(60687),a=s(43210),n=s(31082),l=s(58089),o=s(68589),i=s(45994);let d=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"}))});var c=s(73559),m=s(45700);let x=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))});var p=s(64364),u=s(59168),h=s(62392),g=s(52238),f=s(19682),v=s(2969);function y(){let[e,t]=(0,a.useState)(null),[s,y]=(0,a.useState)(null),[b,j]=(0,a.useState)(null),[N,w]=(0,a.useState)([]),[k,C]=(0,a.useState)(null),[_,S]=(0,a.useState)([]),[A,L]=(0,a.useState)(!0),[R,q]=(0,a.useState)(null),[E,M]=(0,a.useState)("30"),[P,$]=(0,a.useState)(""),[I,D]=(0,a.useState)(!1),[O,B]=(0,a.useState)(""),[F,K]=(0,a.useState)(""),[W,T]=(0,a.useState)(100),[H,Z]=(0,a.useState)(!1),G=(0,a.useCallback)(async()=>{try{let e;L(!0),q(null);let s=new URLSearchParams,r=new Date;O&&F?(e=new Date(O),r=new Date(F),s.append("startDate",e.toISOString()),s.append("endDate",r.toISOString())):E&&((e=new Date).setDate(e.getDate()-parseInt(E)),s.append("startDate",e.toISOString())),P&&s.append("customApiConfigId",P);let a=new URLSearchParams,n=r.getTime()-e.getTime(),l=new Date(e.getTime()-n),o=new Date(e.getTime());a.append("startDate",l.toISOString()),a.append("endDate",o.toISOString()),P&&a.append("customApiConfigId",P);let[i,d,c,m]=await Promise.all([fetch(`/api/analytics/summary?${s.toString()}&groupBy=provider`),fetch(`/api/analytics/summary?${s.toString()}&groupBy=model`),fetch(`/api/analytics/summary?${s.toString()}&groupBy=day`),fetch(`/api/analytics/summary?${a.toString()}&groupBy=day`)]);if(!i.ok||!d.ok||!c.ok)throw Error("Failed to fetch analytics data");let x=await i.json(),p=await d.json(),u=await c.json(),h=m.ok?await m.json():null;t(x),y(p),C(h);let g=u.grouped_data.map(e=>({period:e.period,cost:e.cost,requests:e.requests,tokens:e.input_tokens+e.output_tokens}));w(g)}catch(e){q(e.message)}finally{L(!1)}},[E,P,O,F]),z=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(e),U=e=>new Intl.NumberFormat("en-US").format(e),V=(e,t)=>{if(0===t)return{percentage:0,isPositive:!0};let s=(e-t)/t*100;return{percentage:Math.abs(s),isPositive:s>=0}};if(A)return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},t))})]});if(R)return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold mb-6",children:"\uD83D\uDCCA Advanced Analytics"}),(0,r.jsxs)("div",{className:"card p-6 text-center",children:[(0,r.jsxs)("p",{className:"text-red-600 mb-4",children:["Error loading analytics: ",R]}),(0,r.jsx)("button",{onClick:G,className:"btn-primary",children:"Retry"})]})]});let X=e?.summary;X&&(X.total_cost,X.successful_requests);let Y=X?X.total_cost/parseInt(E)*30:0,J=(()=>{let t=[],s=e?.summary;if(!s)return t;let r=s.total_cost/parseInt(E)*30;return r>.8*W&&t.push({type:r>W?"danger":"warning",title:r>W?"Budget Exceeded":"Budget Warning",message:`Projected monthly cost: ${z(r)} (Budget: ${z(W)})`,value:`${(r/W*100).toFixed(0)}%`}),s.success_rate<95&&t.push({type:s.success_rate<90?"danger":"warning",title:"Low Success Rate",message:`Current success rate is ${s.success_rate.toFixed(1)}%. Consider reviewing failed requests.`,value:`${s.success_rate.toFixed(1)}%`}),s.average_cost_per_request>.01&&t.push({type:"warning",title:"High Cost Per Request",message:`Average cost per request is ${z(s.average_cost_per_request)}. Consider optimizing model usage.`,value:z(s.average_cost_per_request)}),t})(),Q=(()=>{let t=[],r=e?.summary;e?.grouped_data;let a=s?.grouped_data||[];if(!r)return t;let i=a.sort((e,t)=>t.cost-e.cost)[0];if(i&&i.cost>.3*r.total_cost){let e=.2*i.cost;t.push({type:"cost_optimization",title:"Optimize Expensive Model Usage",description:`${i.name} accounts for ${(i.cost/r.total_cost*100).toFixed(1)}% of your costs. Consider using a more cost-effective alternative for simpler tasks.`,potential_savings:e,icon:n.A})}r.success_rate<98&&t.push({type:"performance",title:"Improve Request Reliability",description:`Your success rate is ${r.success_rate.toFixed(1)}%. Implement retry logic and error handling to improve reliability.`,icon:l.A});let d=(r.total_input_tokens+r.total_output_tokens)/r.total_requests;return d>1e3&&t.push({type:"efficiency",title:"Optimize Token Usage",description:`Average ${U(d)} tokens per request. Consider breaking down large prompts or using more efficient models.`,icon:o.A}),t})(),ee=k?.summary,et=ee?V(X?.total_cost||0,ee.total_cost):null,es=ee?V(X?.total_requests||0,ee.total_requests):null;return(0,r.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Analytics Overview"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track your LLM usage, costs, and performance across all providers"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("select",{value:E,onChange:e=>M(e.target.value),className:"px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,r.jsx)("option",{value:"7",children:"Last 7 days"}),(0,r.jsx)("option",{value:"30",children:"Last 30 days"}),(0,r.jsx)("option",{value:"90",children:"Last 90 days"})]}),(0,r.jsxs)("button",{onClick:()=>D(!I),className:"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2 inline"}),"Filters"]})]})]}),I&&(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Advanced Filters"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time Range"}),(0,r.jsxs)("select",{value:E,onChange:e=>M(e.target.value),className:"input-field",children:[(0,r.jsx)("option",{value:"7",children:"Last 7 days"}),(0,r.jsx)("option",{value:"30",children:"Last 30 days"}),(0,r.jsx)("option",{value:"90",children:"Last 90 days"}),(0,r.jsx)("option",{value:"365",children:"Last year"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Configuration"}),(0,r.jsxs)("select",{value:P,onChange:e=>$(e.target.value),className:"input-field",children:[(0,r.jsx)("option",{value:"",children:"All Configurations"}),_.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),(0,r.jsx)("input",{type:"date",value:O,onChange:e=>B(e.target.value),className:"input-field"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date"}),(0,r.jsx)("input",{type:"date",value:F,onChange:e=>K(e.target.value),className:"input-field"})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex space-x-3",children:[(0,r.jsx)("button",{onClick:G,className:"btn-primary",children:"Apply Filters"}),(0,r.jsx)("button",{onClick:()=>{M("30"),$(""),B(""),K("")},className:"btn-secondary",children:"Reset Filters"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Usage Analytics"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Cost trends over time"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full mr-2"}),(0,r.jsx)("span",{className:"text-gray-600",children:"Cost"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-cyan-500 rounded-full mr-2"}),(0,r.jsx)("span",{className:"text-gray-600",children:"Requests"})]})]})]}),(0,r.jsx)(({data:e})=>{if(!e.length)return(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No data available"})]})});let t=Math.max(...e.map(e=>e.cost)),s=Math.min(...e.map(e=>e.cost)),a=Math.max(...e.map(e=>e.requests)),n=Math.min(...e.map(e=>e.requests)),l=t-s||1,o=a-n||1;return(0,r.jsx)("div",{className:"relative h-64 w-full",children:(0,r.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 600 200",children:[(0,r.jsxs)("defs",{children:[(0,r.jsxs)("linearGradient",{id:"costGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#8b5cf6",stopOpacity:"0.3"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#8b5cf6",stopOpacity:"0.05"})]}),(0,r.jsxs)("linearGradient",{id:"requestGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#06b6d4",stopOpacity:"0.3"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#06b6d4",stopOpacity:"0.05"})]}),(0,r.jsx)("pattern",{id:"grid",width:"40",height:"40",patternUnits:"userSpaceOnUse",children:(0,r.jsx)("path",{d:"M 40 0 L 0 0 0 40",fill:"none",stroke:"#f1f5f9",strokeWidth:"0.5"})})]}),(0,r.jsx)("rect",{width:"600",height:"200",fill:"url(#grid)"}),(0,r.jsx)("polygon",{fill:"url(#costGradient)",points:`0,200 ${e.map((t,r)=>{let a=r/Math.max(e.length-1,1)*600,n=200-(t.cost-s)/l*160;return`${a},${n}`}).join(" ")} 600,200`}),(0,r.jsx)("polygon",{fill:"url(#requestGradient)",points:`0,200 ${e.map((t,s)=>{let r=s/Math.max(e.length-1,1)*600,a=200-(t.requests-n)/o*160;return`${r},${a}`}).join(" ")} 600,200`}),(0,r.jsx)("polyline",{fill:"none",stroke:"#8b5cf6",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",points:e.map((t,r)=>{let a=r/Math.max(e.length-1,1)*600,n=200-(t.cost-s)/l*160;return`${a},${n}`}).join(" ")}),(0,r.jsx)("polyline",{fill:"none",stroke:"#06b6d4",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",points:e.map((t,s)=>{let r=s/Math.max(e.length-1,1)*600,a=200-(t.requests-n)/o*160;return`${r},${a}`}).join(" ")}),e.map((t,a)=>{let i=a/Math.max(e.length-1,1)*600,d=200-(t.cost-s)/l*160,c=200-(t.requests-n)/o*160;return(0,r.jsxs)("g",{children:[(0,r.jsx)("circle",{cx:i,cy:d,r:"3",fill:"#8b5cf6",stroke:"white",strokeWidth:"2",className:"hover:r-5 transition-all duration-200 cursor-pointer",children:(0,r.jsx)("title",{children:`${t.period}: ${z(t.cost)}`})}),(0,r.jsx)("circle",{cx:i,cy:c,r:"3",fill:"#06b6d4",stroke:"white",strokeWidth:"2",className:"hover:r-5 transition-all duration-200 cursor-pointer",children:(0,r.jsx)("title",{children:`${t.period}: ${U(t.requests)} requests`})})]},a)})]})})},{data:N}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900 mb-1",children:z(X?.total_cost||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total spend this period"})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Cost Distribution"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"By provider"})]}),e?.grouped_data.length&&X?(0,r.jsx)(({data:e,total:t})=>{if(!e.length)return(0,r.jsx)("div",{className:"h-48 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No data available"})]})});let s=["#f43f5e","#8b5cf6","#06b6d4","#10b981","#f59e0b","#ef4444"],a=124*Math.PI,n=0;return(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"relative mb-6",children:[(0,r.jsxs)("svg",{width:"160",height:"160",className:"transform -rotate-90",children:[(0,r.jsx)("circle",{cx:"80",cy:"80",r:62,stroke:"#f8fafc",strokeWidth:16,fill:"transparent"}),e.map((e,l)=>{let o=e.cost/t*100,i=`${o/100*a} ${a}`,d=-(n/100*a);return n+=o,(0,r.jsx)("circle",{cx:"80",cy:"80",r:62,stroke:s[l%s.length],strokeWidth:16,strokeDasharray:i,strokeDashoffset:d,fill:"transparent",strokeLinecap:"round",className:"transition-all duration-300 hover:opacity-80 cursor-pointer",style:{filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.1))"}},l)})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Providers"})]})})]}),(0,r.jsx)("div",{className:"space-y-2 w-full",children:e.slice(0,4).map((e,a)=>{let n=(e.cost/t*100).toFixed(1);return(0,r.jsxs)("div",{className:"flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors duration-150",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-3 shadow-sm",style:{backgroundColor:s[a]}}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 capitalize",children:e.name})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-sm font-semibold text-gray-900",children:[n,"%"]}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:z(e.cost)})]})]},a)})})]})},{data:e.grouped_data,total:X.total_cost}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(d,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No provider data available"})]})]})]}),X&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-orange-50 rounded-lg",children:(0,r.jsx)(n.A,{className:"h-5 w-5 text-[#ff6b35]"})}),(0,r.jsx)("div",{className:"text-right",children:et&&(0,r.jsxs)("div",{className:`flex items-center text-sm ${et.isPositive?"text-red-500":"text-green-500"}`,children:[et.isPositive?(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1"}):(0,r.jsx)(x,{className:"h-4 w-4 mr-1"}),et.percentage.toFixed(1),"%"]})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:z(X.total_cost)}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total spend"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[z(X.average_cost_per_request)," avg per request"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:`p-2 rounded-lg ${X.success_rate>=95?"bg-green-50":"bg-red-50"}`,children:(0,r.jsx)(l.A,{className:`h-5 w-5 ${X.success_rate>=95?"text-green-600":"text-red-600"}`})}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:`flex items-center text-sm ${X.success_rate>=95?"text-green-500":"text-red-500"}`,children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),X.success_rate>=95?"Excellent":"Needs attention"]})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900",children:[X.success_rate.toFixed(1),"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Success rate"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[U(X.successful_requests)," of ",U(X.total_requests)," requests"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-purple-600"})}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:"flex items-center text-sm text-purple-500",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Tokens"]})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:U(X.total_input_tokens+X.total_output_tokens)}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total tokens"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[U(X.total_input_tokens)," input • ",U(X.total_output_tokens)," output"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-indigo-50 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-indigo-600"})}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:`flex items-center text-sm ${Y>W?"text-red-500":"text-green-500"}`,children:[Y>W?(0,r.jsx)(u.A,{className:"h-4 w-4 mr-1"}):(0,r.jsx)(l.A,{className:"h-4 w-4 mr-1"}),Y>W?"Over budget":"On track"]})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:z(Y)}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Projected monthly"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Budget: ",z(W)]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Provider Performance"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Detailed breakdown by provider"})]}),e?.grouped_data.length?(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,r.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Provider"}),(0,r.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Cost"}),(0,r.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Requests"}),(0,r.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Share"})]})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-50",children:e.grouped_data.sort((e,t)=>t.cost-e.cost).map((e,t)=>{let s=["#ff6b35","#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6"],a=(e.cost/(X?.total_cost||1)*100).toFixed(1);return(0,r.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,r.jsx)("td",{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-3",style:{backgroundColor:s[t%s.length]}}),(0,r.jsx)("span",{className:"font-medium text-gray-900 capitalize",children:e.name})]})}),(0,r.jsx)("td",{className:"py-4 text-right font-semibold text-gray-900",children:z(e.cost)}),(0,r.jsx)("td",{className:"py-4 text-right text-gray-600",children:U(e.requests)}),(0,r.jsx)("td",{className:"py-4 text-right",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[a,"%"]})})]},e.name)})})]})}):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(d,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{children:"No provider data available"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Top Models"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Most expensive models by cost"})]}),s?.grouped_data.length?(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,r.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Model"}),(0,r.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Cost"}),(0,r.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Requests"}),(0,r.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Avg/Request"})]})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-50",children:s.grouped_data.sort((e,t)=>t.cost-e.cost).slice(0,5).map((e,t)=>{let s=["#ff6b35","#3b82f6","#10b981","#f59e0b","#ef4444"],a=e.cost/Math.max(e.requests,1);return(0,r.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,r.jsx)("td",{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-3",style:{backgroundColor:s[t%s.length]}}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.name.length>20?e.name.substring(0,20)+"...":e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[U(e.input_tokens+e.output_tokens)," tokens"]})]})]})}),(0,r.jsx)("td",{className:"py-4 text-right font-semibold text-gray-900",children:z(e.cost)}),(0,r.jsx)("td",{className:"py-4 text-right text-gray-600",children:U(e.requests)}),(0,r.jsx)("td",{className:"py-4 text-right text-gray-600",children:z(a)})]},e.name)})})]})}):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{children:"No model data available"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Performance Insights"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 rounded-lg bg-green-50",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-2",children:X?U(X.total_input_tokens+X.total_output_tokens):0}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Total Tokens Processed"}),(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:X?`${U(X.total_input_tokens)} in • ${U(X.total_output_tokens)} out`:""})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-lg bg-blue-50",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:X?U(X.total_requests):0}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Total API Requests"}),(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:X?`${(X.total_requests/parseInt(E)).toFixed(1)} per day avg`:""})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-lg bg-purple-50",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:X?z(X.average_cost_per_request):"$0.00"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Average Cost per Request"}),(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:"Across all providers"})]})]})]}),(J.length>0||Q.length>0)&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[J.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-2 text-[#ff6b35]"}),"Alerts & Warnings"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Important notifications about your usage"})]}),(0,r.jsx)("div",{className:"space-y-4",children:J.map((e,t)=>(0,r.jsx)("div",{className:`p-4 rounded-xl border-l-4 ${"danger"===e.type?"border-red-500 bg-red-50":"warning"===e.type?"border-yellow-500 bg-yellow-50":"border-blue-500 bg-blue-50"}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(u.A,{className:`h-5 w-5 mr-3 mt-0.5 ${"danger"===e.type?"text-red-600":"warning"===e.type?"text-yellow-600":"text-blue-600"}`}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),e.value&&(0,r.jsx)("p",{className:`text-lg font-bold mt-2 ${"danger"===e.type?"text-red-600":"warning"===e.type?"text-yellow-600":"text-blue-600"}`,children:e.value})]})]})},t))})]}),Q.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 mr-2 text-yellow-500"}),"Smart Recommendations"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"AI-powered optimization suggestions"})]}),(0,r.jsx)("div",{className:"space-y-4",children:Q.map((e,t)=>(0,r.jsx)("div",{className:"p-4 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-white shadow-sm mr-4",children:(0,r.jsx)(e.icon,{className:"h-4 w-4 text-blue-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),e.potential_savings&&(0,r.jsxs)("div",{className:"mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs bg-green-100 text-green-800",children:[(0,r.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Save ",z(e.potential_savings)]})]})]})},t))})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,r.jsx)(n.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Budget Management"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Monitor and control your spending"})]}),(0,r.jsx)("button",{onClick:()=>Z(!H),className:"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200",children:H?"Hide Settings":"Configure"})]}),H&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-xl",children:[(0,r.jsxs)("div",{className:"max-w-xs",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Monthly Budget (USD)"}),(0,r.jsx)("input",{type:"number",value:W,onChange:e=>T(parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",placeholder:"100.00",step:"0.01",min:"0"})]}),(0,r.jsxs)("div",{className:"mt-3 text-xs text-gray-600",children:[(0,r.jsx)("p",{children:"• Alerts trigger at 80% (warning) and 100% (danger) of budget"}),(0,r.jsxs)("p",{children:["• Current projection: ",(0,r.jsx)("strong",{children:z(Y)})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-blue-50",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:z(W)}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Monthly Budget"})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-orange-50",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-[#ff6b35]",children:z(Y)}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Projected Spend"})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-green-50",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:z(Math.max(0,W-Y))}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:Y>W?"Over Budget":"Remaining"})]})]})]}),ee&&(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Period Comparison"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Performance vs previous period"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-gray-50",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Cost Change"}),(0,r.jsxs)("div",{className:`text-2xl font-bold ${et?.isPositive?"text-red-600":"text-green-600"}`,children:[et?.isPositive?"+":"-",et?.percentage.toFixed(1),"%"]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:[z(ee.total_cost)," → ",z(X?.total_cost||0)]})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-gray-50",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Request Volume"}),(0,r.jsxs)("div",{className:`text-2xl font-bold ${es?.isPositive?"text-green-600":"text-red-600"}`,children:[es?.isPositive?"+":"-",es?.percentage.toFixed(1),"%"]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:[U(ee.total_requests)," → ",U(X?.total_requests||0)]})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-gray-50",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Efficiency"}),(0,r.jsx)("div",{className:`text-2xl font-bold ${(X?.average_cost_per_request||0)<ee.average_cost_per_request?"text-green-600":"text-red-600"}`,children:(X?.average_cost_per_request||0)<ee.average_cost_per_request?"↑":"↓"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:[z(ee.average_cost_per_request)," → ",z(X?.average_cost_per_request||0)]})]})]})]})]})})}function b(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},t))})]}),children:(0,r.jsx)(y,{})})}},64845:(e,t,s)=>{Promise.resolve().then(s.bind(s,35291))},68589:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,221,1658,7437],()=>s(23425));module.exports=r})();