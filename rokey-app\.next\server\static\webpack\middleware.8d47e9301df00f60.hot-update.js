"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname\n    const pathname = req.nextUrl.pathname;\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>pathname === route || pathname.startsWith(route));\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the session with error handling for network issues\n    let session = null;\n    try {\n        const { data: { session: authSession } } = await supabase.auth.getSession();\n        session = authSession;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier').eq('id', session.user.id).single();\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // If we can't fetch profile due to network issues, allow access\n                return res;\n            }\n            // If no profile or inactive subscription, redirect to pricing\n            if (!profile || profile.subscription_status !== 'active') {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});