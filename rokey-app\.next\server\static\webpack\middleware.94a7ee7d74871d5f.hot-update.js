"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('Middleware: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname\n    const pathname = req.nextUrl.pathname;\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>pathname === route || pathname.startsWith(route));\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the session with error handling for network issues\n    let session = null;\n    try {\n        const { data: { session: authSession } } = await supabase.auth.getSession();\n        session = authSession;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier').eq('id', session.user.id).single();\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // If we can't fetch profile due to network issues, allow access\n                return res;\n            }\n            // If no profile or inactive subscription, redirect to pricing\n            if (!profile || profile.subscription_status !== 'active') {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});